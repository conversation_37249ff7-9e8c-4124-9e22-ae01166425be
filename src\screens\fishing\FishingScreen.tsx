import React, { useState, useEffect, useRef } from 'react';
import {
	View,
	StyleSheet,
	Dimensions,
	TouchableOpacity,
	Animated,
	PanResponder,
} from 'react-native';
import { Text } from '@/components';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Fish {
	id: string;
	type: 'small' | 'medium' | 'large';
	difficulty: number; // 1-10
	x: number;
	y: number;
	speed: number;
}

type GameState =
	| 'ready'
	| 'casting'
	| 'waiting'
	| 'fish_approaching'
	| 'fishing'
	| 'caught'
	| 'escaped';

export const FishingScreen = () => {
	const [gameState, setGameState] = useState<GameState>('ready');
	const [currentFish, setCurrentFish] = useState<Fish | null>(null);
	const [fishingPower, setFishingPower] = useState(0);
	const [isHolding, setIsHolding] = useState(false);
	const [score, setScore] = useState(0);

	// Animation values
	const fishOpacity = useRef(new Animated.Value(0)).current;
	const fishPosition = useRef(new Animated.Value(0)).current;
	const hookPosition = useRef(new Animated.Value(0)).current; // Hook animation
	const rodTip = useRef(new Animated.Value(0)).current; // Rod tip animation
	const powerBarWidth = useRef(new Animated.Value(0)).current;

	// Generate random fish
	const generateFish = (): Fish => {
		const types: Fish['type'][] = ['small', 'medium', 'large'];
		const type = types[Math.floor(Math.random() * types.length)];

		let difficulty: number;
		switch (type) {
			case 'small':
				difficulty = Math.floor(Math.random() * 3) + 1; // 1-3
				break;
			case 'medium':
				difficulty = Math.floor(Math.random() * 3) + 4; // 4-6
				break;
			case 'large':
				difficulty = Math.floor(Math.random() * 4) + 7; // 7-10
				break;
		}

		return {
			id: Math.random().toString(),
			type,
			difficulty,
			x: Math.random() * (SCREEN_WIDTH - 100) + 50,
			y: SCREEN_HEIGHT * 0.3 + Math.random() * 100, // Fish in pond area
			speed: Math.random() * 2 + 1,
		};
	};

	// Cast the hook
	const castHook = () => {
		if (gameState !== 'ready') return;

		setGameState('casting');

		// Animate hook going down
		Animated.timing(hookPosition, {
			toValue: 1,
			duration: 1000,
			useNativeDriver: true,
		}).start(() => {
			setGameState('waiting');

			// Random delay before fish approaches (2-5 seconds)
			const delay = Math.random() * 3000 + 2000;

			setTimeout(() => {
				const fish = generateFish();
				setCurrentFish(fish);
				setGameState('fish_approaching');

				// Animate fish approaching hook
				Animated.sequence([
					Animated.timing(fishOpacity, {
						toValue: 0.8,
						duration: 500,
						useNativeDriver: true,
					}),
					Animated.timing(fishPosition, {
						toValue: 1,
						duration: 1500,
						useNativeDriver: true,
					}),
				]).start(() => {
					// Fish stays near hook for limited time
					setTimeout(() => {
						if (gameState === 'fish_approaching') {
							// Fish swims away
							Animated.timing(fishOpacity, {
								toValue: 0,
								duration: 500,
								useNativeDriver: true,
							}).start(() => {
								resetGame();
							});
						}
					}, 3000); // 3 seconds to react
				});
			}, delay);
		});
	};

	// Handle fishing attempt
	const handleFishingPress = () => {
		if (gameState !== 'fish_approaching') return;

		setGameState('fishing');
		setIsHolding(true);
		setFishingPower(0);

		// Start power animation
		Animated.timing(powerBarWidth, {
			toValue: 1,
			duration: 2000,
			useNativeDriver: false,
		}).start();
	};

	const handleFishingRelease = () => {
		if (gameState !== 'fishing' || !currentFish) return;

		setIsHolding(false);

		// Calculate success based on power and fish difficulty
		const requiredPower = currentFish.difficulty * 10; // 10-100
		const powerDifference = Math.abs(fishingPower - requiredPower);
		const tolerance = 20; // Allow some margin for error

		if (powerDifference <= tolerance) {
			// Success!
			setGameState('caught');
			setScore((prev) => prev + currentFish.difficulty * 10);

			// Animate success - rod tip bends
			Animated.sequence([
				Animated.timing(rodTip, {
					toValue: 1,
					duration: 300,
					useNativeDriver: true,
				}),
				Animated.timing(rodTip, {
					toValue: 0,
					duration: 300,
					useNativeDriver: true,
				}),
			]).start(() => {
				setTimeout(() => {
					resetGame();
				}, 1000);
			});
		} else {
			// Failed
			setGameState('escaped');
			setTimeout(() => {
				resetGame();
			}, 1000);
		}

		// Reset power bar
		Animated.timing(powerBarWidth, {
			toValue: 0,
			duration: 300,
			useNativeDriver: false,
		}).start();
	};

	const resetGame = () => {
		setGameState('ready');
		setCurrentFish(null);
		setFishingPower(0);

		// Reset animations
		fishOpacity.setValue(0);
		fishPosition.setValue(0);
		hookPosition.setValue(0);
		rodTip.setValue(0);
		powerBarWidth.setValue(0);
	};

	// Update fishing power while holding
	useEffect(() => {
		let interval: ReturnType<typeof setInterval>;

		if (isHolding && gameState === 'fishing') {
			interval = setInterval(() => {
				setFishingPower((prev) => {
					const newPower = prev + 2;
					return newPower > 100 ? 0 : newPower; // Reset to 0 if over 100
				});
			}, 50);
		}

		return () => {
			if (interval) clearInterval(interval);
		};
	}, [isHolding, gameState]);

	// Start the game
	useEffect(() => {
		// Game starts in ready state
	}, []);

	return (
		<View style={styles.container}>
			{/* Pond/Water - now in front view */}
			<View style={styles.pond} />

			{/* Fishing Rod - from bottom perspective */}
			<Animated.View
				style={[
					styles.fishingRod,
					{
						transform: [
							{
								rotate: rodTip.interpolate({
									inputRange: [0, 1],
									outputRange: ['0deg', '10deg'],
								}),
							},
						],
					},
				]}>
				<View style={styles.rod} />

				{/* Fishing Line with Hook */}
				<Animated.View
					style={[
						styles.lineContainer,
						{
							transform: [
								{
									translateY: hookPosition.interpolate({
										inputRange: [0, 1],
										outputRange: [0, SCREEN_HEIGHT * 0.4],
									}),
								},
							],
						},
					]}>
					<View style={styles.line} />
					<View style={styles.hook} />
				</Animated.View>
			</Animated.View>

			{/* Fish in pond */}
			{currentFish && (
				<Animated.View
					style={[
						styles.fishShadow,
						{
							left: currentFish.x,
							top: currentFish.y,
							opacity: fishOpacity,
							transform: [
								{
									translateX: fishPosition.interpolate({
										inputRange: [0, 1],
										outputRange: [0, -30], // Fish moves toward hook
									}),
								},
								{
									translateY: fishPosition.interpolate({
										inputRange: [0, 1],
										outputRange: [0, -20],
									}),
								},
							],
						},
					]}>
					<View
						style={[
							styles.fish,
							currentFish.type === 'small' && styles.smallFish,
							currentFish.type === 'medium' && styles.mediumFish,
							currentFish.type === 'large' && styles.largeFish,
						]}
					/>
				</Animated.View>
			)}

			{/* Cast Hook Button */}
			{gameState === 'ready' && (
				<TouchableOpacity style={styles.castButton} onPress={castHook} activeOpacity={0.8}>
					<Text style={styles.castButtonText}>🎣 THẢ CÂU</Text>
				</TouchableOpacity>
			)}

			{/* Fishing Button - appears when fish approaches */}
			{gameState === 'fish_approaching' && (
				<TouchableOpacity
					style={styles.fishingButton}
					onPressIn={handleFishingPress}
					onPressOut={handleFishingRelease}
					activeOpacity={0.8}>
					<Text style={styles.fishingButtonText}>🎣 CÂU!</Text>
				</TouchableOpacity>
			)}

			{/* Power Bar */}
			{gameState === 'fishing' && currentFish && (
				<View style={styles.powerBarContainer}>
					<Text style={styles.powerBarLabel}>
						Lực câu: {fishingPower}% (Cần: {currentFish.difficulty * 10}%)
					</Text>
					<View style={styles.powerBarBackground}>
						<View style={[styles.powerBar, { width: `${fishingPower}%` }]} />
						<View
							style={[
								styles.targetZone,
								{
									left: `${Math.max(0, currentFish.difficulty * 10 - 10)}%`,
									width: '20%',
								},
							]}
						/>
					</View>
				</View>
			)}

			{/* Game Status */}
			<View style={styles.statusContainer}>
				<Text style={styles.scoreText}>Điểm: {score}</Text>
				<Text style={styles.statusText}>
					{gameState === 'ready' && 'Bấm để thả câu xuống hồ'}
					{gameState === 'casting' && 'Đang thả câu...'}
					{gameState === 'waiting' && 'Đang chờ cá...'}
					{gameState === 'fish_approaching' && 'Có cá! Bấm để câu!'}
					{gameState === 'fishing' && 'Giữ để tăng lực!'}
					{gameState === 'caught' && 'Câu được rồi! 🎉'}
					{gameState === 'escaped' && 'Cá chạy mất rồi! 😢'}
				</Text>
			</View>

			{/* Reset Button */}
			{(gameState === 'caught' || gameState === 'escaped') && (
				<TouchableOpacity style={styles.resetButton} onPress={resetGame}>
					<Text style={styles.resetButtonText}>Câu tiếp</Text>
				</TouchableOpacity>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#87CEEB', // Sky blue
	},
	pond: {
		position: 'absolute',
		top: SCREEN_HEIGHT * 0.1,
		left: SCREEN_WIDTH * 0.1,
		right: SCREEN_WIDTH * 0.1,
		height: SCREEN_HEIGHT * 0.5,
		backgroundColor: '#4682B4', // Steel blue
		borderRadius: 20,
		opacity: 0.8,
		borderWidth: 3,
		borderColor: '#2F4F4F', // Dark slate gray
	},
	fishingRod: {
		position: 'absolute',
		bottom: 50,
		left: SCREEN_WIDTH * 0.4,
		width: 100,
		height: 150,
	},
	rod: {
		position: 'absolute',
		bottom: 0,
		left: 45,
		width: 10,
		height: 120,
		backgroundColor: '#8B4513', // Saddle brown
		borderRadius: 5,
	},
	lineContainer: {
		position: 'absolute',
		bottom: 110,
		left: 48,
		alignItems: 'center',
	},
	line: {
		width: 2,
		height: 50,
		backgroundColor: '#000',
	},
	hook: {
		width: 8,
		height: 8,
		backgroundColor: '#C0C0C0', // Silver
		borderRadius: 4,
		marginTop: -2,
	},
	fishShadow: {
		position: 'absolute',
		width: 60,
		height: 40,
	},
	fish: {
		width: '100%',
		height: '100%',
		borderRadius: 20,
		opacity: 0.6,
	},
	smallFish: {
		backgroundColor: '#FFD700', // Gold
		width: 30,
		height: 20,
	},
	mediumFish: {
		backgroundColor: '#FF6347', // Tomato
		width: 45,
		height: 30,
	},
	largeFish: {
		backgroundColor: '#FF4500', // Orange red
		width: 60,
		height: 40,
	},
	fishingButton: {
		position: 'absolute',
		bottom: SCREEN_HEIGHT * 0.3,
		alignSelf: 'center',
		backgroundColor: '#FF6B6B',
		paddingHorizontal: 30,
		paddingVertical: 15,
		borderRadius: 25,
		elevation: 5,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.25,
		shadowRadius: 3.84,
	},
	fishingButtonText: {
		color: 'white',
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	powerBarContainer: {
		position: 'absolute',
		bottom: SCREEN_HEIGHT * 0.15,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
	powerBarLabel: {
		color: 'white',
		fontSize: 16,
		fontWeight: 'bold',
		marginBottom: 10,
		textAlign: 'center',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 10,
		paddingVertical: 5,
		borderRadius: 5,
	},
	powerBarBackground: {
		width: '100%',
		height: 20,
		backgroundColor: 'rgba(255,255,255,0.3)',
		borderRadius: 10,
		position: 'relative',
	},
	powerBar: {
		height: '100%',
		backgroundColor: '#4CAF50',
		borderRadius: 10,
	},
	targetZone: {
		position: 'absolute',
		top: 0,
		height: '100%',
		backgroundColor: 'rgba(255,255,0,0.5)',
		borderRadius: 10,
	},
	statusContainer: {
		position: 'absolute',
		top: 50,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
	scoreText: {
		color: 'white',
		fontSize: 24,
		fontWeight: 'bold',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 15,
		paddingVertical: 8,
		borderRadius: 20,
		marginBottom: 10,
	},
	statusText: {
		color: 'white',
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 15,
		paddingVertical: 8,
		borderRadius: 15,
	},
	resetButton: {
		position: 'absolute',
		bottom: 50,
		alignSelf: 'center',
		backgroundColor: '#4CAF50',
		paddingHorizontal: 25,
		paddingVertical: 12,
		borderRadius: 20,
		elevation: 3,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 1 },
		shadowOpacity: 0.22,
		shadowRadius: 2.22,
	},
	resetButtonText: {
		color: 'white',
		fontSize: 16,
		fontWeight: 'bold',
		textAlign: 'center',
	},
});
