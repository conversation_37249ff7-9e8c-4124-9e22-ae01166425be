import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
	useSharedValue,
	useAnimatedStyle,
	withTiming,
	withSpring,
	withSequence,
	withDelay,
	runOnJS,
	interpolate,
	Extrapolation,
} from 'react-native-reanimated';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import { Text } from '@/components';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Fish {
	id: string;
	type: 'small' | 'medium' | 'large';
	difficulty: number; // 1-10
	x: number;
	y: number;
	speed: number;
}

type GameState =
	| 'ready'
	| 'casting'
	| 'waiting'
	| 'fish_approaching'
	| 'fishing'
	| 'caught'
	| 'escaped';

export const FishingScreen = () => {
	const [gameState, setGameState] = useState<GameState>('ready');
	const [currentFish, setCurrentFish] = useState<Fish | null>(null);
	const [fishingPower, setFishingPower] = useState(0);
	const [isHolding, setIsHolding] = useState(false);
	const [score, setScore] = useState(0);

	// Reanimated shared values
	const fishOpacity = useSharedValue(0);
	const fishPositionX = useSharedValue(0);
	const fishPositionY = useSharedValue(0);
	const hookPosition = useSharedValue(0);
	const rodTip = useSharedValue(0);
	const powerBarProgress = useSharedValue(0);
	const buttonScale = useSharedValue(1);
	const fishScale = useSharedValue(1);

	// Generate random fish
	const generateFish = (): Fish => {
		const types: Fish['type'][] = ['small', 'medium', 'large'];
		const type = types[Math.floor(Math.random() * types.length)];

		let difficulty: number;
		switch (type) {
			case 'small':
				difficulty = Math.floor(Math.random() * 3) + 1; // 1-3
				break;
			case 'medium':
				difficulty = Math.floor(Math.random() * 3) + 4; // 4-6
				break;
			case 'large':
				difficulty = Math.floor(Math.random() * 4) + 7; // 7-10
				break;
		}

		return {
			id: Math.random().toString(),
			type,
			difficulty,
			x: Math.random() * (SCREEN_WIDTH - 100) + 50,
			y: SCREEN_HEIGHT * 0.3 + Math.random() * 100, // Fish in pond area
			speed: Math.random() * 2 + 1,
		};
	};

	// Cast the hook
	const castHook = () => {
		if (gameState !== 'ready') return;

		setGameState('casting');

		// Animate hook going down with spring
		hookPosition.value = withSpring(1, { damping: 15 }, (finished) => {
			if (finished) {
				runOnJS(setGameState)('waiting');

				// Random delay before fish approaches (2-5 seconds)
				const delay = Math.random() * 3000 + 2000;

				setTimeout(() => {
					const fish = generateFish();
					runOnJS(setCurrentFish)(fish);
					runOnJS(setGameState)('fish_approaching');

					// Animate fish appearing and approaching
					fishOpacity.value = withTiming(0.8, { duration: 500 });
					fishPositionX.value = withDelay(500, withTiming(-30, { duration: 1500 }));
					fishPositionY.value = withDelay(500, withTiming(-20, { duration: 1500 }));
					fishScale.value = withSequence(
						withTiming(1.2, { duration: 200 }),
						withTiming(1, { duration: 200 })
					);

					// Fish stays near hook for limited time
					setTimeout(() => {
						if (gameState === 'fish_approaching') {
							// Fish swims away
							fishOpacity.value = withTiming(0, { duration: 500 }, (finished) => {
								if (finished) {
									runOnJS(resetGame)();
								}
							});
						}
					}, 3000); // 3 seconds to react
				}, delay);
			}
		});
	};

	// Handle fishing attempt with gesture
	const handleFishingPress = () => {
		if (gameState !== 'fish_approaching') return;

		setGameState('fishing');
		setIsHolding(true);
		setFishingPower(0);

		// Start power animation
		powerBarProgress.value = withTiming(1, { duration: 2000 });
		buttonScale.value = withSpring(0.9);
	};

	const handleFishingRelease = () => {
		if (gameState !== 'fishing' || !currentFish) return;

		setIsHolding(false);
		buttonScale.value = withSpring(1);

		// Calculate success based on power and fish difficulty
		const requiredPower = currentFish.difficulty * 10; // 10-100
		const powerDifference = Math.abs(fishingPower - requiredPower);
		const tolerance = 20; // Allow some margin for error

		if (powerDifference <= tolerance) {
			// Success!
			setGameState('caught');
			setScore((prev) => prev + currentFish.difficulty * 10);

			// Animate success - rod tip bends with spring
			rodTip.value = withSequence(
				withSpring(1, { damping: 10 }),
				withDelay(300, withSpring(0, { damping: 15 }))
			);

			// Fish celebration animation
			fishScale.value = withSequence(
				withTiming(1.5, { duration: 200 }),
				withTiming(0, { duration: 300 })
			);

			setTimeout(() => {
				resetGame();
			}, 1000);
		} else {
			// Failed - fish escapes
			setGameState('escaped');

			// Fish escape animation
			fishOpacity.value = withTiming(0, { duration: 300 });
			fishPositionX.value = withTiming(100, { duration: 500 });

			setTimeout(() => {
				resetGame();
			}, 1000);
		}

		// Reset power bar
		powerBarProgress.value = withTiming(0, { duration: 300 });
	};

	const resetGame = () => {
		setGameState('ready');
		setCurrentFish(null);
		setFishingPower(0);

		// Reset animations with smooth transitions
		fishOpacity.value = 0;
		fishPositionX.value = 0;
		fishPositionY.value = 0;
		hookPosition.value = 0;
		rodTip.value = 0;
		powerBarProgress.value = 0;
		buttonScale.value = 1;
		fishScale.value = 1;
	};

	// Update fishing power while holding
	useEffect(() => {
		let interval: ReturnType<typeof setInterval>;

		if (isHolding && gameState === 'fishing') {
			interval = setInterval(() => {
				setFishingPower((prev) => {
					const newPower = prev + 2;
					return newPower > 100 ? 0 : newPower; // Reset to 0 if over 100
				});
			}, 50);
		}

		return () => {
			if (interval) clearInterval(interval);
		};
	}, [isHolding, gameState]);

	// Animated styles
	const rodAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [
				{
					rotate: `${interpolate(rodTip.value, [0, 1], [0, 10])}deg`,
				},
			],
		};
	});

	const hookAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [
				{
					translateY: interpolate(
						hookPosition.value,
						[0, 1],
						[0, SCREEN_HEIGHT * 0.4],
						Extrapolation.CLAMP
					),
				},
			],
		};
	});

	const fishAnimatedStyle = useAnimatedStyle(() => {
		return {
			opacity: fishOpacity.value,
			transform: [
				{
					translateX: fishPositionX.value,
				},
				{
					translateY: fishPositionY.value,
				},
				{
					scale: fishScale.value,
				},
			],
		};
	});

	const buttonAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [
				{
					scale: buttonScale.value,
				},
			],
		};
	});

	const powerBarAnimatedStyle = useAnimatedStyle(() => {
		return {
			width: `${interpolate(powerBarProgress.value, [0, 1], [0, 100])}%`,
		};
	});

	// Gesture for fishing button
	const fishingGesture = Gesture.Tap()
		.onBegin(() => {
			runOnJS(handleFishingPress)();
		})
		.onFinalize(() => {
			runOnJS(handleFishingRelease)();
		});

	// Cast hook gesture
	const castGesture = Gesture.Tap().onEnd(() => {
		runOnJS(castHook)();
	});

	// Reset game gesture
	const resetGesture = Gesture.Tap().onEnd(() => {
		runOnJS(resetGame)();
	});

	// Start the game
	useEffect(() => {
		// Game starts in ready state
	}, []);

	return (
		<GestureHandlerRootView style={styles.container}>
			{/* Pond/Water - now in front view */}
			<View style={styles.pond} />

			{/* Fishing Rod - from bottom perspective */}
			<Animated.View style={[styles.fishingRod, rodAnimatedStyle]}>
				<View style={styles.rod} />

				{/* Fishing Line with Hook */}
				<Animated.View style={[styles.lineContainer, hookAnimatedStyle]}>
					<View style={styles.line} />
					<View style={styles.hook} />
				</Animated.View>
			</Animated.View>

			{/* Fish in pond */}
			{currentFish && (
				<Animated.View
					style={[
						styles.fishShadow,
						{
							left: currentFish.x,
							top: currentFish.y,
						},
						fishAnimatedStyle,
					]}>
					<View
						style={[
							styles.fish,
							currentFish.type === 'small' && styles.smallFish,
							currentFish.type === 'medium' && styles.mediumFish,
							currentFish.type === 'large' && styles.largeFish,
						]}
					/>
				</Animated.View>
			)}

			{/* Cast Hook Button */}
			{gameState === 'ready' && (
				<GestureDetector gesture={castGesture}>
					<Animated.View style={[styles.castButton, buttonAnimatedStyle]}>
						<Text style={styles.castButtonText}>🎣 THẢ CÂU</Text>
					</Animated.View>
				</GestureDetector>
			)}

			{/* Fishing Button - appears when fish approaches */}
			{gameState === 'fish_approaching' && (
				<GestureDetector gesture={fishingGesture}>
					<Animated.View style={[styles.fishingButton, buttonAnimatedStyle]}>
						<Text style={styles.fishingButtonText}>🎣 CÂU!</Text>
					</Animated.View>
				</GestureDetector>
			)}

			{/* Power Bar */}
			{gameState === 'fishing' && currentFish && (
				<View style={styles.powerBarContainer}>
					<Text style={styles.powerBarLabel}>
						Lực câu: {fishingPower}% (Cần: {currentFish.difficulty * 10}%)
					</Text>
					<View style={styles.powerBarBackground}>
						<Animated.View style={[styles.powerBar, powerBarAnimatedStyle]} />
						<View
							style={[
								styles.targetZone,
								{
									left: `${Math.max(0, currentFish.difficulty * 10 - 10)}%`,
									width: '20%',
								},
							]}
						/>
					</View>
				</View>
			)}

			{/* Game Status */}
			<View style={styles.statusContainer}>
				<Text style={styles.scoreText}>Điểm: {score}</Text>
				<Text style={styles.statusText}>
					{gameState === 'ready' && 'Bấm để thả câu xuống hồ'}
					{gameState === 'casting' && 'Đang thả câu...'}
					{gameState === 'waiting' && 'Đang chờ cá...'}
					{gameState === 'fish_approaching' && 'Có cá! Bấm để câu!'}
					{gameState === 'fishing' && 'Giữ để tăng lực!'}
					{gameState === 'caught' && 'Câu được rồi! 🎉'}
					{gameState === 'escaped' && 'Cá chạy mất rồi! 😢'}
				</Text>
			</View>

			{/* Reset Button */}
			{(gameState === 'caught' || gameState === 'escaped') && (
				<GestureDetector gesture={resetGesture}>
					<Animated.View style={styles.resetButton}>
						<Text style={styles.resetButtonText}>Câu tiếp</Text>
					</Animated.View>
				</GestureDetector>
			)}
		</GestureHandlerRootView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#87CEEB', // Sky blue
	},
	pond: {
		position: 'absolute',
		top: SCREEN_HEIGHT * 0.1,
		left: SCREEN_WIDTH * 0.1,
		right: SCREEN_WIDTH * 0.1,
		height: SCREEN_HEIGHT * 0.5,
		backgroundColor: '#4682B4', // Steel blue
		borderRadius: 20,
		opacity: 0.8,
		borderWidth: 3,
		borderColor: '#2F4F4F', // Dark slate gray
	},
	fishingRod: {
		position: 'absolute',
		bottom: 50,
		left: SCREEN_WIDTH * 0.4,
		width: 100,
		height: 150,
	},
	rod: {
		position: 'absolute',
		bottom: 0,
		left: 45,
		width: 10,
		height: 120,
		backgroundColor: '#8B4513', // Saddle brown
		borderRadius: 5,
	},
	lineContainer: {
		position: 'absolute',
		bottom: 110,
		left: 48,
		alignItems: 'center',
	},
	line: {
		width: 2,
		height: 50,
		backgroundColor: '#000',
	},
	hook: {
		width: 8,
		height: 8,
		backgroundColor: '#C0C0C0', // Silver
		borderRadius: 4,
		marginTop: -2,
	},
	fishShadow: {
		position: 'absolute',
		width: 60,
		height: 40,
	},
	fish: {
		width: '100%',
		height: '100%',
		borderRadius: 20,
		opacity: 0.7,
	},
	smallFish: {
		backgroundColor: '#FFD700', // Gold
		width: 30,
		height: 20,
	},
	mediumFish: {
		backgroundColor: '#FF6347', // Tomato
		width: 45,
		height: 30,
	},
	largeFish: {
		backgroundColor: '#FF4500', // Orange red
		width: 60,
		height: 40,
	},
	castButton: {
		position: 'absolute',
		bottom: SCREEN_HEIGHT * 0.25,
		alignSelf: 'center',
		backgroundColor: '#4CAF50',
		paddingHorizontal: 30,
		paddingVertical: 15,
		borderRadius: 25,
		elevation: 5,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.25,
		shadowRadius: 3.84,
	},
	castButtonText: {
		color: 'white',
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	fishingButton: {
		position: 'absolute',
		bottom: SCREEN_HEIGHT * 0.25,
		alignSelf: 'center',
		backgroundColor: '#FF6B6B',
		paddingHorizontal: 30,
		paddingVertical: 15,
		borderRadius: 25,
		elevation: 5,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.25,
		shadowRadius: 3.84,
	},
	fishingButtonText: {
		color: 'white',
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	powerBarContainer: {
		position: 'absolute',
		bottom: SCREEN_HEIGHT * 0.15,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
	powerBarLabel: {
		color: 'white',
		fontSize: 16,
		fontWeight: 'bold',
		marginBottom: 10,
		textAlign: 'center',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 10,
		paddingVertical: 5,
		borderRadius: 5,
	},
	powerBarBackground: {
		width: '100%',
		height: 20,
		backgroundColor: 'rgba(255,255,255,0.3)',
		borderRadius: 10,
		position: 'relative',
	},
	powerBar: {
		height: '100%',
		backgroundColor: '#4CAF50',
		borderRadius: 10,
	},
	targetZone: {
		position: 'absolute',
		top: 0,
		height: '100%',
		backgroundColor: 'rgba(255,255,0,0.5)',
		borderRadius: 10,
	},
	statusContainer: {
		position: 'absolute',
		top: 50,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
	scoreText: {
		color: 'white',
		fontSize: 24,
		fontWeight: 'bold',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 15,
		paddingVertical: 8,
		borderRadius: 20,
		marginBottom: 10,
	},
	statusText: {
		color: 'white',
		fontSize: 18,
		fontWeight: 'bold',
		textAlign: 'center',
		backgroundColor: 'rgba(0,0,0,0.5)',
		paddingHorizontal: 15,
		paddingVertical: 8,
		borderRadius: 15,
	},
	resetButton: {
		position: 'absolute',
		bottom: 50,
		alignSelf: 'center',
		backgroundColor: '#4CAF50',
		paddingHorizontal: 25,
		paddingVertical: 12,
		borderRadius: 20,
		elevation: 3,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 1 },
		shadowOpacity: 0.22,
		shadowRadius: 2.22,
	},
	resetButtonText: {
		color: 'white',
		fontSize: 16,
		fontWeight: 'bold',
		textAlign: 'center',
	},
});
